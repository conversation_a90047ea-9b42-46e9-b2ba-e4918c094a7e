import { Target } from '~/shared/types/Target';

import { useTargetActions } from '../hooks/useTargetActions';

import { AcceptTargetButton } from './AcceptTargetButton';
import { TargetBadges } from './TargetBadges';

interface TargetFooterProps {
  target: Target;
  isOnCatalogWithTabs?: boolean;
  showBadgesInProposalTab?: boolean;
  onAcceptTarget?: (targetUid: string, agree?: boolean) => void;
  agreeStatus?: boolean | null;
  isTargetAccepted?: (targetUid: string) => boolean;
  isTargetLoading?: (targetUid: string) => boolean;
}

export function TargetFooter({
  target,
  isOnCatalogWithTabs = false,
  showBadgesInProposalTab = false,
  onAcceptTarget,
  agreeStatus,
  isTargetAccepted,
  isTargetLoading,
}: TargetFooterProps) {
  const {
    shouldShowIAgree,
    shouldShowDontAgree,
    shouldShowBadges,
    shouldShowAcceptButton,
    agreeStatus: _hookAgreeStatus,
    isAccepted,
    isLoading,
    handleAcceptTarget,
  } = useTargetActions({
    target,
    isOnCatalogWithTabs,
    showBadgesInProposalTab,
    onAcceptTarget,
    agreeStatus,
    isTargetAccepted,
    isTargetLoading,
  });

  if (!isOnCatalogWithTabs) {
    return null;
  }

  // Debug logging
  console.log('TargetFooter Debug:', {
    targetUid: target.uid,
    agreeStatus,
    shouldShowBadges,
    shouldShowAcceptButton,
    hasAgreeStatus: agreeStatus !== null && agreeStatus !== undefined,
  });

  const badges = shouldShowBadges ? (
    <TargetBadges
      agreeStatus={agreeStatus}
      shouldShowIAgree={shouldShowIAgree}
      shouldShowDontAgree={shouldShowDontAgree}
    />
  ) : null;

  const button = shouldShowAcceptButton ? (
    <AcceptTargetButton
      agreeStatus={agreeStatus}
      isAccepted={isAccepted}
      isLoading={isLoading}
      onAccept={handleAcceptTarget}
    />
  ) : null;

  if (badges || button) {
    return (
      <div className="flex flex-col items-end gap-2">
        {button}
        {badges}
      </div>
    );
  }

  return null;
}
