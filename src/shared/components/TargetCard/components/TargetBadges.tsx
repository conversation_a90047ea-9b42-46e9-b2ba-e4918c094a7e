import { CheckCircle, XCircle } from 'react-bootstrap-icons';
import { Button, Typography } from '@ghq-abi/design-system-v2';

interface TargetBadgesProps {
  agreeStatus?: boolean | null;
  shouldShowIAgree?: boolean;
  shouldShowDontAgree?: boolean;
}

export function TargetBadges({
  agreeStatus,
  shouldShowIAgree: _shouldShowIAgree,
  shouldShowDontAgree: _shouldShowDontAgree,
}: TargetBadgesProps) {
  const showAgreeBadge = agreeStatus === true;
  const showDisagreeBadge = agreeStatus === false;

  // Debug logging to understand what's happening
  console.log('TargetBadges Debug:', {
    agreeStatus,
    showAgreeBadge,
    showDisagreeBadge,
    type: typeof agreeStatus,
  });

  if (agreeStatus === null || agreeStatus === undefined) {
    return null;
  }

  return (
    <div className="flex flex-col gap-2">
      {showAgreeBadge && (
        <Button
          variant="secondary"
          size="sm"
          border="default"
          round="md"
          iconLeft={<CheckCircle className="text-green-500" />}
          className="border border-green-500 text-green-500 bg-transparent cursor-default"
        >
          <Typography
            variant="body-sm-medium"
            className="font-semibold text-green-500"
          >
            I Agree
          </Typography>
        </Button>
      )}
      {showDisagreeBadge && (
        <Button
          variant="secondary"
          size="sm"
          border="default"
          round="md"
          iconLeft={<XCircle className="text-red-500" />}
          className="border border-red-500 text-red-500 bg-transparent cursor-default"
        >
          <Typography
            variant="body-sm-medium"
            className="font-semibold text-red-500"
          >
            Don&apos;t Agree
          </Typography>
        </Button>
      )}
    </div>
  );
}
